import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def setup_openai_client():
    """
    Set up the OpenAI client with API key and model from .env
    """
    try:
        import openai
        api_key = os.getenv('OPENAI_KEY')
        model = os.getenv('OPENAI_MODEL')
        if not api_key:
            return None, None, "ERROR: OPENAI_KEY not found in .env file"
        if not model:
            return None, None, "ERROR: OPENAI_MODEL not found in .env file"
        openai.api_key = api_key
        return openai, model, "SUCCESS"
    except ImportError:
        return None, None, "ERROR: openai library not installed. Run: pip install openai"
    except Exception as e:
        return None, None, f"ERROR setting up OpenAI: {str(e)}" 