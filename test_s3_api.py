#!/usr/bin/env python3
"""
Test script for S3-integrated Psychometrist Portal API
Demonstrates file upload, analysis, and report generation using S3
"""

import requests
import json
from pathlib import Path

# API base URL
BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test API health check"""
    print("🔍 Testing API Health Check...")
    
    response = requests.get(f"{BASE_URL}/health")
    
    if response.status_code == 200:
        health_data = response.json()
        print(f"✅ API Status: {health_data['status']}")
        print(f"🤖 AI System: {health_data['ai_system']['groq_status']}")
        print(f"☁️  S3 System: {health_data['s3_system']['status']}")
        print(f"🪣 S3 Bucket: {health_data['s3_system']['bucket']}")
        return True
    else:
        print(f"❌ Health check failed: {response.status_code}")
        return False

def test_file_upload():
    """Test file upload to S3"""
    print("\n📤 Testing File Upload to S3...")
    
    # Create a test document
    test_content = """
    PSYCHOLOGICAL ASSESSMENT REPORT
    
    Patient: Test Patient
    Age: 25 years old
    Gender: Female
    
    Background:
    Test Patient, a 25-year-old female, was referred for psychological assessment due to anxiety symptoms.
    
    Clinical Assessment:
    Psychological evaluation indicates moderate anxiety (GAD-7 score: 12).
    Cognitive abilities assessment shows average intelligence (WISC-V FSIQ: 105).
    
    Treatment Plan:
    Weekly individual CBT sessions to address anxiety symptoms.
    Monthly family therapy sessions for support.
    
    Progress Notes:
    Patient shows good engagement in treatment and motivation for change.
    """
    
    # Save test file
    test_file_path = "test_psychological_report.txt"
    with open(test_file_path, 'w') as f:
        f.write(test_content)
    
    try:
        # Upload file
        with open(test_file_path, 'rb') as f:
            files = {'file': ('test_psychological_report.txt', f, 'text/plain')}
            response = requests.post(f"{BASE_URL}/upload", files=files)
        
        if response.status_code == 200:
            upload_data = response.json()
            print(f"✅ File uploaded successfully!")
            print(f"📄 File ID: {upload_data['file_id']}")
            print(f"☁️  S3 Key: {upload_data['s3_key']}")
            print(f"📊 Size: {upload_data['size']} bytes")
            return upload_data['s3_key']
        else:
            print(f"❌ Upload failed: {response.status_code} - {response.text}")
            return None
    
    finally:
        # Clean up test file
        if Path(test_file_path).exists():
            Path(test_file_path).unlink()

def test_document_analysis(s3_key):
    """Test document analysis from S3"""
    print(f"\n🧠 Testing Document Analysis from S3...")
    print(f"📄 Analyzing: {s3_key}")
    
    response = requests.post(
        f"{BASE_URL}/analyze-document",
        params={"s3_key": s3_key}
    )
    
    if response.status_code == 200:
        analysis_data = response.json()
        print(f"✅ Analysis completed successfully!")
        print(f"📋 Document Type: {analysis_data['analysis'].get('document_type', 'Unknown')}")
        
        patient_info = analysis_data['analysis'].get('patient_info', {})
        if patient_info.get('name'):
            print(f"👤 Patient: {patient_info['name']}")
            print(f"🎂 Age: {patient_info.get('age', 'Unknown')}")
        
        key_scores = analysis_data['analysis'].get('key_scores', [])
        if key_scores:
            print(f"📊 Key Scores: {', '.join(key_scores[:3])}")
        
        print(f"💾 Analysis saved to: {analysis_data['analysis_saved_to']}")
        return analysis_data['analysis']
    else:
        print(f"❌ Analysis failed: {response.status_code} - {response.text}")
        return None

def test_list_s3_files():
    """Test listing S3 files"""
    print(f"\n📂 Testing S3 File Listing...")
    
    response = requests.get(f"{BASE_URL}/s3/files?folder=uploads")
    
    if response.status_code == 200:
        files_data = response.json()
        print(f"✅ Found {files_data['total_files']} files in uploads folder")
        
        for file_info in files_data['files'][:3]:  # Show first 3 files
            print(f"   📄 {file_info['filename']} ({file_info['size']} bytes)")
        
        return True
    else:
        print(f"❌ File listing failed: {response.status_code} - {response.text}")
        return False

def test_presigned_url(s3_key):
    """Test generating presigned URL"""
    print(f"\n🔗 Testing Presigned URL Generation...")
    
    response = requests.get(f"{BASE_URL}/s3/download/{s3_key}")
    
    if response.status_code == 200:
        url_data = response.json()
        print(f"✅ Presigned URL generated successfully!")
        print(f"⏰ Expires in: {url_data['expires_in']} seconds")
        print(f"🔗 URL: {url_data['download_url'][:100]}...")
        return url_data['download_url']
    else:
        print(f"❌ URL generation failed: {response.status_code} - {response.text}")
        return None

def main():
    """Run all tests"""
    print("🚀 TESTING S3-INTEGRATED PSYCHOMETRIST PORTAL API")
    print("=" * 60)
    
    # Test 1: Health Check
    if not test_health_check():
        print("❌ Health check failed. Stopping tests.")
        return
    
    # Test 2: File Upload
    s3_key = test_file_upload()
    if not s3_key:
        print("❌ File upload failed. Stopping tests.")
        return
    
    # Test 3: Document Analysis
    analysis_result = test_document_analysis(s3_key)
    if not analysis_result:
        print("❌ Document analysis failed.")
    
    # Test 4: List S3 Files
    test_list_s3_files()
    
    # Test 5: Generate Presigned URL
    test_presigned_url(s3_key)
    
    print("\n" + "=" * 60)
    print("🎉 S3 API TESTING COMPLETED!")
    print("\n📋 SUMMARY:")
    print("✅ Health Check - API and S3 connection working")
    print("✅ File Upload - Documents uploaded to S3 successfully")
    print("✅ AI Analysis - Documents analyzed and results saved to S3")
    print("✅ File Management - S3 file operations working")
    print("✅ Security - Presigned URLs for secure file access")
    
    print(f"\n🌐 API Documentation: {BASE_URL}/docs")
    print(f"🔍 Health Status: {BASE_URL}/health")

if __name__ == "__main__":
    main()
