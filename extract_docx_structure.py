import os
from docx import Document

SAMPLE_REPORTS_DIR = 'sample_reports'

def extract_headings(docx_path):
    doc = Document(docx_path)
    headings = []
    for para in doc.paragraphs:
        if para.style.name.startswith('Heading') or para.style.name in ['Title', 'Subtitle']:
            headings.append((para.style.name, para.text.strip()))
    return headings

def main():
    for filename in os.listdir(SAMPLE_REPORTS_DIR):
        if filename.lower().endswith('.docx'):
            path = os.path.join(SAMPLE_REPORTS_DIR, filename)
            print(f'\n--- {filename} ---')
            headings = extract_headings(path)
            for style, text in headings:
                print(f'[{style}] {text}')

if __name__ == '__main__':
    main() 