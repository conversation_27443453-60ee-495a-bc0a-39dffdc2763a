#!/usr/bin/env python3
"""
Test the updated search-similar API with AI-based matching and filters
"""

import requests
import json
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test API health"""
    print("🔍 Testing API Health...")
    response = requests.get(f"{BASE_URL}/health")
    
    if response.status_code == 200:
        health = response.json()
        print(f"✅ API Status: {health['status']}")
        print(f"🤖 AI System: {health['ai_system']['groq_status']}")
        print(f"☁️  S3 System: {health['s3_system']['status']}")
        return True
    else:
        print(f"❌ Health check failed: {response.status_code}")
        return False

def test_list_reports():
    """Get list of reports to use for testing"""
    print("📂 Getting list of reports...")
    response = requests.get(f"{BASE_URL}/reports")
    
    if response.status_code == 200:
        data = response.json()
        reports = data['reports']
        print(f"✅ Found {len(reports)} reports")
        
        if reports:
            for i, report in enumerate(reports[:3], 1):
                print(f"   {i}. {report['filename']} ({report['size']} bytes)")
            return reports[0]['s3_key'] if reports else None
        else:
            print("❌ No reports found")
            return None
    else:
        print(f"❌ Failed to list reports: {response.status_code}")
        return None

def test_search_similar(target_s3_key):
    """Test the AI-based similarity search with filters"""
    print(f"\n🔍 Testing Similarity Search...")
    print(f"🎯 Target: {target_s3_key}")
    
    # Test without filters
    print("\n📊 Test 1: Search without filters")
    response = requests.post(
        f"{BASE_URL}/search-similar",
        json={
            "target_s3_key": target_s3_key,
            "max_results": 5
        }
    )
    
    if response.status_code == 200:
        data = response.json()
        results = data['search_results']
        
        print(f"✅ Search completed successfully!")
        print(f"📊 Reports compared: {results['total_compared']}")
        print(f"🔍 Search method: {results['search_method']}")
        
        similar_reports = results['similar_reports']
        print(f"\n🏆 Top {len(similar_reports)} Similar Reports:")
        
        for i, report in enumerate(similar_reports, 1):
            metadata = report['metadata']
            similarity = report['overall_similarity']
            breakdown = report.get('similarity_breakdown', {})
            
            print(f"\n{i}. {report['filename']} - {similarity}% overall similarity")
            print(f"   👤 {metadata.get('age', 'Unknown')} y/o {metadata.get('gender', 'Unknown')}")
            
            if metadata.get('test_types'):
                print(f"   🧪 Tests: {', '.join(metadata['test_types'][:3])}")
            
            if metadata.get('diagnoses'):
                print(f"   🏥 Diagnoses: {', '.join(metadata['diagnoses'][:3])}")
            
            if breakdown:
                print(f"   📊 Breakdown: Demographics:{breakdown.get('demographic_match', 0)}% Diagnoses:{breakdown.get('diagnosis_match', 0)}% Symptoms:{breakdown.get('symptom_match', 0)}%")
        
        # Test with filters
        print(f"\n📊 Test 2: Search with filters")
        filters = {
            "age_min": 10,
            "age_max": 15,
            "gender": "Male",
            "date_from": (datetime.now() - timedelta(days=365)).isoformat(),  # Last year
            "keywords": ["ADHD", "anxiety"]
        }
        
        response_filtered = requests.post(
            f"{BASE_URL}/search-similar",
            json={
                "target_s3_key": target_s3_key,
                "max_results": 3,
                "filters": filters
            }
        )
        
        if response_filtered.status_code == 200:
            filtered_data = response_filtered.json()
            filtered_results = filtered_data['search_results']
            
            print(f"✅ Filtered search completed!")
            print(f"📊 Reports compared: {filtered_results['total_compared']}")
            print(f"🎯 Filters applied: {filtered_results['filters_applied']}")
            
            filtered_reports = filtered_results['similar_reports']
            if filtered_reports:
                print(f"\n🔍 Filtered Results:")
                for i, report in enumerate(filtered_reports, 1):
                    metadata = report['metadata']
                    similarity = report['overall_similarity']
                    print(f"   {i}. {report['filename']} - {similarity}% ({metadata.get('age', 'Unknown')} y/o {metadata.get('gender', 'Unknown')})")
                    
                    if report.get('key_similarities'):
                        print(f"      🔑 Similarities: {', '.join(report['key_similarities'])}")
                    
                    if report.get('useful_for'):
                        print(f"      💡 Useful for: {report['useful_for']}")
            else:
                print("   ❌ No reports matched the filters")
        else:
            print(f"❌ Filtered search failed: {response_filtered.status_code}")
        
        return True
    else:
        print(f"❌ Search failed: {response.status_code}")
        print(f"Error: {response.text}")
        return False

def main():
    """Run the similarity search test"""
    print("🧪 TESTING AI-BASED SIMILARITY SEARCH API")
    print("=" * 60)
    
    # Check health first
    if not test_health_check():
        print("❌ API not healthy. Stopping test.")
        return
    
    print()
    
    # Get a report to use as target
    target_s3_key = test_list_reports()
    if not target_s3_key:
        print("❌ No reports available for testing. Upload some reports first.")
        return
    
    # Test similarity search
    success = test_search_similar(target_s3_key)
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 SIMILARITY SEARCH API TEST COMPLETED SUCCESSFULLY!")
        print("\n📋 Summary:")
        print("✅ AI-based similarity matching working")
        print("✅ Filtering system working (age, gender, date, keywords)")
        print("✅ Detailed similarity breakdowns")
        print("✅ Key similarities and usefulness explanations")
        print("✅ S3 integration working")
    else:
        print("❌ SIMILARITY SEARCH API TEST FAILED!")
    
    print(f"\n🌐 API Documentation: {BASE_URL}/docs")
    print("\n🎯 The search now uses AI for better matching!")
    print("   - Semantic understanding of reports")
    print("   - Detailed similarity breakdowns")
    print("   - Advanced filtering options")
    print("   - Key similarities and usefulness")

if __name__ == "__main__":
    main()
