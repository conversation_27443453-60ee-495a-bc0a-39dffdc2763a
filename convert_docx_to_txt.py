import os
from docx import Document

SAMPLE_REPORTS_DIR = 'sample_reports'

def convert_docx_to_txt(docx_path, txt_path):
    doc = Document(docx_path)
    with open(txt_path, 'w', encoding='utf-8') as txt_file:
        for para in doc.paragraphs:
            txt_file.write(para.text + '\n')

def main():
    for filename in os.listdir(SAMPLE_REPORTS_DIR):
        if filename.lower().endswith('.docx'):
            docx_path = os.path.join(SAMPLE_REPORTS_DIR, filename)
            txt_path = os.path.join(SAMPLE_REPORTS_DIR, filename.replace('.docx', '.txt'))
            convert_docx_to_txt(docx_path, txt_path)
            print(f'Converted {filename} to {txt_path}')

if __name__ == '__main__':
    main() 