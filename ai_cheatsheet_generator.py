#!/usr/bin/env python3
"""
AI-Generated Cheatsheet System for Psychometrist Portal
Analyzes existing reports to generate reusable templates and guidelines
"""

import os
import json
import re
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv
from openai_client import setup_openai_client

# Load environment variables
load_dotenv()

def create_cheatsheet_prompt(report_content):
    """
    Create a prompt for AI to generate cheatsheet templates from a report
    """
    prompt = f"""
You are an expert clinical psychologist analyzing a psychological report to create reusable templates and guidelines.

REPORT CONTENT:
{report_content}

Generate a cheatsheet with reusable templates in JSON format:

{{
    "background_templates": [
        "Template patterns for background sections with placeholders like {{Age}} y/o {{Gender}} presenting with {{Primary_Condition}}..."
    ],
    "clinical_assessment_formats": [
        "Template patterns for clinical assessments like 'Evaluation indicates {{Severity}} {{Condition}} ({{Scale}}: {{Score}})'"
    ],
    "treatment_structures": [
        "Template patterns for treatment plans like 'Approach combines {{Primary_Treatment}} with {{Secondary_Treatment}}'"
    ],
    "academic_templates": [
        "Template patterns for academic sections"
    ],
    "social_development_guides": [
        "Template patterns for social development sections"
    ],
    "key_phrases": [
        "Common clinical phrases and terminology used"
    ],
    "score_interpretation_guides": [
        "Guidelines for interpreting different test scores and ranges"
    ]
}}

GUIDELINES:
- Extract reusable patterns with placeholder variables in {{brackets}}
- Focus on professional clinical language
- Include specific formatting patterns
- Identify common assessment structures
- Create templates that can be reused across similar cases

Return ONLY valid JSON, no other text.
"""
    return prompt

def generate_cheatsheet_with_ai(report_content, client, model):
    """
    Use AI to generate cheatsheet templates from a report
    """
    try:
        prompt = create_cheatsheet_prompt(report_content)
        
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "user", "content": prompt}
            ],
            temperature=0.2,  # Low temperature for consistent templates
            max_tokens=2000
        )
        
        ai_response = response.choices[0].message.content
        
        # Try to parse as JSON
        try:
            # First try direct parsing
            cheatsheet = json.loads(ai_response)
            return cheatsheet, "SUCCESS"
        except json.JSONDecodeError:
            # Try to extract JSON from response
            import re
            json_match = re.search(r'(\{.*\})', ai_response, re.DOTALL)
            if json_match:
                try:
                    cheatsheet = json.loads(json_match.group(1))
                    return cheatsheet, "SUCCESS"
                except json.JSONDecodeError:
                    pass
            
            return {"error": "Could not parse AI response", "raw_response": ai_response}, "ERROR"
    
    except Exception as e:
        print("OpenAI API error in generate_cheatsheet_with_ai:", e)
        if hasattr(e, 'response'):
            print("OpenAI response:", getattr(e.response, 'text', e.response))
        return {"error": str(e)}, "ERROR"

def analyze_reports_for_cheatsheet(reports_directory="reports"):
    """
    Analyze existing reports to generate comprehensive cheatsheet
    """
    print("📋 AI-GENERATED CHEATSHEET SYSTEM")
    print("=" * 60)
    
    # Step 1: Find all report files
    reports_path = Path(reports_directory)
    if not reports_path.exists():
        return {"error": f"Reports directory not found: {reports_directory}"}
    
    report_files = list(reports_path.glob("*_psychological_report.txt"))
    
    if not report_files:
        return {"error": "No psychological reports found"}
    
    print(f"📄 Step 1: Found {len(report_files)} reports to analyze")
    
    # Step 2: Set up AI
    print("�� Step 2: Setting up OpenAI...")
    client, model, status = setup_openai_client()
    
    if status != "SUCCESS":
        return {"error": status}
    
    print(f"✅ OpenAI ready (Model: {model})")
    
    # Step 3: Analyze reports and generate cheatsheets
    print("🧠 Step 3: Generating cheatsheet templates...")
    
    all_cheatsheets = []
    
    for i, report_file in enumerate(report_files[:3], 1):  # Limit to 3 reports for demo
        print(f"   📋 Analyzing report {i}: {report_file.name}")
        
        # Read report content
        try:
            with open(report_file, 'r', encoding='utf-8') as f:
                report_content = f.read()
        except Exception as e:
            print(f"   ❌ Error reading {report_file.name}: {e}")
            continue
        
        # Generate cheatsheet
        cheatsheet, ai_status = generate_cheatsheet_with_ai(report_content, client, model)
        
        if ai_status == "SUCCESS":
            cheatsheet["source_report"] = report_file.name
            cheatsheet["generated_date"] = datetime.now().isoformat()
            all_cheatsheets.append(cheatsheet)
            print(f"   ✅ Cheatsheet generated successfully")
        else:
            print(f"   ❌ Failed to generate cheatsheet: {cheatsheet.get('error', 'Unknown error')}")
    
    # Step 4: Combine and organize cheatsheets
    print("📊 Step 4: Combining cheatsheets...")
    
    combined_cheatsheet = combine_cheatsheets(all_cheatsheets)
    
    # Step 5: Save cheatsheet
    output_file = f"ai_generated_cheatsheet_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(combined_cheatsheet, f, indent=2)
        
        print(f"💾 Cheatsheet saved to: {output_file}")
        
        return {
            "cheatsheet": combined_cheatsheet,
            "output_file": output_file,
            "reports_analyzed": len(all_cheatsheets),
            "status": "SUCCESS"
        }
    
    except Exception as e:
        return {"error": f"Failed to save cheatsheet: {e}"}

def combine_cheatsheets(cheatsheets):
    """
    Combine multiple cheatsheets into one comprehensive guide
    """
    combined = {
        "generated_date": datetime.now().isoformat(),
        "source_reports": [cs.get("source_report", "Unknown") for cs in cheatsheets],
        "background_templates": [],
        "clinical_assessment_formats": [],
        "treatment_structures": [],
        "academic_templates": [],
        "social_development_guides": [],
        "key_phrases": [],
        "score_interpretation_guides": []
    }
    
    # Combine all templates from different reports
    for cheatsheet in cheatsheets:
        for key in combined.keys():
            if key in cheatsheet and isinstance(cheatsheet[key], list):
                combined[key].extend(cheatsheet[key])
    
    # Remove duplicates and sort
    for key in combined.keys():
        if isinstance(combined[key], list) and key not in ["source_reports"]:
            combined[key] = sorted(list(set(combined[key])))
    
    return combined

def display_cheatsheet(cheatsheet):
    """
    Display cheatsheet in a user-friendly format
    """
    print("\n" + "=" * 80)
    print("📋 AI-GENERATED CHEATSHEET")
    print("=" * 80)
    
    print(f"Generated: {cheatsheet.get('generated_date', 'Unknown')}")
    print(f"Source Reports: {', '.join(cheatsheet.get('source_reports', []))}")
    print()
    
    sections = [
        ("Background Templates", "background_templates"),
        ("Clinical Assessment Formats", "clinical_assessment_formats"),
        ("Treatment Structures", "treatment_structures"),
        ("Academic Templates", "academic_templates"),
        ("Social Development Guides", "social_development_guides"),
        ("Key Phrases", "key_phrases"),
        ("Score Interpretation Guides", "score_interpretation_guides")
    ]
    
    for section_name, section_key in sections:
        items = cheatsheet.get(section_key, [])
        if items:
            print(f"📝 {section_name}")
            print("-" * 40)
            for i, item in enumerate(items[:3], 1):  # Show first 3 items
                print(f"   {i}. {item}")
            if len(items) > 3:
                print(f"   ... and {len(items) - 3} more")
            print()
    
    print("=" * 80)

def main():
    """
    Test the AI cheatsheet generator
    """
    print("🚀 TESTING AI-GENERATED CHEATSHEET SYSTEM")
    print("=" * 80)
    
    result = analyze_reports_for_cheatsheet()
    
    if "error" in result:
        print(f"❌ ERROR: {result['error']}")
    else:
        print(f"✅ Successfully generated cheatsheet from {result['reports_analyzed']} reports")
        
        # Display the cheatsheet
        display_cheatsheet(result["cheatsheet"])
        
        print(f"\n💾 Full cheatsheet saved to: {result['output_file']}")
    
    print("\n🎉 CHEATSHEET GENERATION TEST FINISHED!")

if __name__ == "__main__":
    main()
