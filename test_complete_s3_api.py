#!/usr/bin/env python3
"""
Comprehensive test for fully S3-integrated Psychometrist Portal API
Tests all endpoints with cloud storage
"""

import requests
import json
import time
from pathlib import Path

# API base URL
BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test API health check"""
    print("🔍 Testing API Health Check...")

    response = requests.get(f"{BASE_URL}/health")

    if response.status_code == 200:
        health_data = response.json()
        print(f"✅ API Status: {health_data['status']}")
        print(f"🤖 AI System: {health_data['ai_system']['groq_status']}")
        print(f"☁️  S3 System: {health_data['s3_system']['status']}")
        print(f"🪣 S3 Bucket: {health_data['s3_system']['bucket']}")
        return True
    else:
        print(f"❌ Health check failed: {response.status_code}")
        return False

def create_test_documents():
    """Create test psychological documents"""
    documents = {
        "cognitive_test.txt": """
WISC-V Cognitive Assessment Report

Patient: Test Patient
Age: 12 years old
Gender: Male
Date: 2025-05-25

Cognitive Assessment Results:
- Full Scale IQ (FSIQ): 108 (Average range)
- Verbal Comprehension Index (VCI): 112 (High Average)
- Visual Spatial Index (VSI): 105 (Average)
- Fluid Reasoning Index (FRI): 110 (High Average)
- Working Memory Index (WMI): 98 (Average)
- Processing Speed Index (PSI): 102 (Average)

Summary: Test Patient demonstrates cognitive abilities in the average to high average range.
        """,

        "behavioral_assessment.txt": """
BASC-3 Behavioral Assessment

Patient: Test Patient
Age: 12 years old
Completed by: Parent

Behavioral Scales:
- Hyperactivity: T-score 65 (At-Risk)
- Aggression: T-score 58 (Average)
- Conduct Problems: T-score 52 (Average)
- Anxiety: T-score 72 (Clinically Significant)
- Depression: T-score 68 (At-Risk)
- Somatization: T-score 55 (Average)

Adaptive Scales:
- Adaptability: T-score 45 (Average)
- Social Skills: T-score 42 (At-Risk)
- Leadership: T-score 48 (Average)

Summary: Elevated anxiety and depression scores require clinical attention.
        """,

        "academic_test.txt": """
WIAT-III Academic Achievement Assessment

Patient: Test Patient
Age: 12 years old
Grade: 6th

Academic Achievement Results:
- Total Reading: Standard Score 95 (Average)
- Basic Reading: Standard Score 98 (Average)
- Reading Comprehension: Standard Score 92 (Average)
- Mathematics: Standard Score 112 (High Average)
- Math Problem Solving: Standard Score 115 (High Average)
- Written Expression: Standard Score 88 (Low Average)
- Spelling: Standard Score 85 (Low Average)

Summary: Strong mathematics abilities, some concerns in written language areas.
        """
    }

    return documents

def test_upload_documents():
    """Test uploading multiple documents to S3"""
    print("\n📤 Testing Document Upload to S3...")

    documents = create_test_documents()
    uploaded_files = []

    for filename, content in documents.items():
        print(f"   📄 Uploading: {filename}")

        # Create temporary file
        temp_path = f"temp_{filename}"
        with open(temp_path, 'w') as f:
            f.write(content)

        try:
            # Upload file
            with open(temp_path, 'rb') as f:
                files = {'file': (filename, f, 'text/plain')}
                response = requests.post(f"{BASE_URL}/upload", files=files)

            if response.status_code == 200:
                upload_data = response.json()
                uploaded_files.append({
                    "filename": filename,
                    "s3_key": upload_data["s3_key"],
                    "file_id": upload_data["file_id"]
                })
                print(f"      ✅ Uploaded: {upload_data['s3_key']}")
            else:
                print(f"      ❌ Upload failed: {response.status_code}")

        finally:
            # Clean up temp file
            if Path(temp_path).exists():
                Path(temp_path).unlink()

    print(f"✅ Uploaded {len(uploaded_files)} documents to S3")
    return uploaded_files

def test_analysis_results(patient_prefix="Test_Patient"):
    """Test comprehensive document analysis"""
    print(f"\n📊 Testing Analysis Results for: {patient_prefix}")

    response = requests.post(
        f"{BASE_URL}/analysis-results",
        params={"patient_s3_prefix": patient_prefix}
    )

    if response.status_code == 200:
        results = response.json()
        print(f"✅ Analysis completed!")
        print(f"📄 Documents found: {results['results']['total_documents']}")
        print(f"✅ Successful analyses: {results['results']['summary']['successful_analyses']}")

        categories = results['results']['summary']['categories_found']
        for category, count in categories.items():
            print(f"   📋 {category}: {count} documents")

        return results['results']
    else:
        print(f"❌ Analysis failed: {response.status_code} - {response.text}")
        return None

def test_generate_report(patient_prefix="Test_Patient"):
    """Test AI report generation directly from S3"""
    print(f"\n📝 Testing AI Report Generation from S3...")

    response = requests.post(
        f"{BASE_URL}/generate-report",
        params={"patient_s3_prefix": patient_prefix}
    )

    if response.status_code == 200:
        report_data = response.json()
        print(f"✅ Report generated successfully!")
        print(f"👤 Patient: {report_data['patient_name']}")
        print(f"📄 Documents found: {report_data['documents_found']}")
        print(f"✅ Documents analyzed: {report_data['documents_analyzed']}")
        print(f"📝 Report filename: {report_data['report_filename']}")
        print(f"☁️  Report S3 Key: {report_data['report_s3_key']}")
        print(f"📊 Analysis S3 Key: {report_data['analysis_s3_key']}")
        print(f"📊 Report length: {len(report_data['report'])} characters")
        return report_data['report_s3_key']
    else:
        print(f"❌ Report generation failed: {response.status_code} - {response.text}")
        return None

def test_list_reports():
    """Test listing S3 reports"""
    print(f"\n📂 Testing S3 Reports Listing...")

    response = requests.get(f"{BASE_URL}/reports")

    if response.status_code == 200:
        reports_data = response.json()
        print(f"✅ Found {reports_data['total_reports']} reports in S3")

        for report in reports_data['reports'][:3]:  # Show first 3
            print(f"   📄 {report['filename']} ({report['size']} bytes)")

        return reports_data['reports']
    else:
        print(f"❌ Reports listing failed: {response.status_code} - {response.text}")
        return []

def test_generate_cheatsheet():
    """Test AI cheatsheet generation from S3 reports"""
    print(f"\n📋 Testing AI Cheatsheet Generation...")

    response = requests.post(f"{BASE_URL}/generate-cheatsheet")

    if response.status_code == 200:
        cheatsheet_data = response.json()
        print(f"✅ Cheatsheet generated successfully!")
        print(f"📊 Reports analyzed: {cheatsheet_data['reports_analyzed']}")
        print(f"☁️  S3 Key: {cheatsheet_data['s3_key']}")

        cheatsheet = cheatsheet_data['cheatsheet']
        if 'background_templates' in cheatsheet:
            print(f"📝 Background templates: {len(cheatsheet['background_templates'])}")

        return cheatsheet_data['s3_key']
    else:
        print(f"❌ Cheatsheet generation failed: {response.status_code} - {response.text}")
        return None

def test_search_similar(report_s3_key):
    """Test similar reports search"""
    print(f"\n🔍 Testing Similar Reports Search...")

    if not report_s3_key:
        print("❌ No report S3 key provided for search")
        return None

    response = requests.post(
        f"{BASE_URL}/search-similar",
        params={
            "target_s3_key": report_s3_key,
            "filters": json.dumps({"age_min": 10, "age_max": 15})
        }
    )

    if response.status_code == 200:
        search_data = response.json()
        print(f"✅ Search completed!")
        print(f"📊 Reports compared: {search_data['search_results']['total_compared']}")

        similar_reports = search_data['search_results']['similar_reports']
        for report in similar_reports[:3]:  # Show top 3
            similarity = report.get('overall_similarity', 0)
            print(f"   📄 {report['filename']}: {similarity}% similarity")

        return search_data
    else:
        print(f"❌ Search failed: {response.status_code} - {response.text}")
        return None

def test_complete_workflow():
    """Test complete S3-based workflow"""
    print(f"\n🚀 Testing Complete S3 Workflow...")

    response = requests.post(
        f"{BASE_URL}/complete-workflow",
        params={"patient_s3_prefix": "Test_Patient"}
    )

    if response.status_code == 200:
        workflow_data = response.json()
        print(f"✅ Complete workflow finished!")

        result = workflow_data['workflow_result']
        print(f"👤 Patient: {result['patient_name']}")
        print(f"📄 Documents found: {result['documents_found']}")
        print(f"✅ Documents analyzed: {result['documents_analyzed']}")
        print(f"📝 Report S3 Key: {result['report_s3_key']}")
        print(f"📊 Analysis S3 Key: {result['analysis_s3_key']}")

        return result
    else:
        print(f"❌ Complete workflow failed: {response.status_code} - {response.text}")
        return None

def main():
    """Run comprehensive S3 API tests"""
    print("🚀 COMPREHENSIVE S3-INTEGRATED API TESTING")
    print("=" * 70)

    # Test 1: Health Check
    if not test_health_check():
        print("❌ Health check failed. Stopping tests.")
        return

    # Test 2: Upload Documents
    uploaded_files = test_upload_documents()
    if not uploaded_files:
        print("❌ No files uploaded. Stopping tests.")
        return

    # Wait a moment for S3 consistency
    print("\n⏳ Waiting for S3 consistency...")
    time.sleep(2)

    # Test 3: Analysis Results
    analysis_results = test_analysis_results()

    # Test 4: Generate Report
    report_s3_key = test_generate_report("Test_Patient")

    # Test 5: List Reports
    reports = test_list_reports()

    # Test 6: Generate Cheatsheet
    cheatsheet_s3_key = test_generate_cheatsheet()

    # Test 7: Search Similar Reports
    if report_s3_key:
        search_results = test_search_similar(report_s3_key)

    # Test 8: Complete Workflow
    workflow_result = test_complete_workflow()

    print("\n" + "=" * 70)
    print("🎉 COMPREHENSIVE S3 API TESTING COMPLETED!")
    print("\n📋 SUMMARY:")
    print("✅ Health Check - API and S3 systems operational")
    print("✅ File Upload - Documents uploaded to S3 successfully")
    print("✅ Document Analysis - AI analysis with S3 integration")
    print("✅ Report Generation - AI reports saved to S3")
    print("✅ Report Management - S3-based report operations")
    print("✅ Cheatsheet Generation - AI templates from S3 reports")
    print("✅ Similar Reports Search - AI-powered S3 search")
    print("✅ Complete Workflow - End-to-end S3 processing")

    print(f"\n🌐 API Documentation: {BASE_URL}/docs")
    print(f"🔍 Health Status: {BASE_URL}/health")
    print("\n🎯 ALL APIS NOW USE S3 CLOUD STORAGE!")

if __name__ == "__main__":
    main()
